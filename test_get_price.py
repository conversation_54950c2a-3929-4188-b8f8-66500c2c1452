#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试get_price函数
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from custom_api.custom_api.md.get_price import get_price


def test_get_price_basic():
    """测试基本的get_price功能"""
    print("=" * 50)
    print("测试基本get_price功能")
    print("=" * 50)
    
    # 测试获取EURUSD价格
    result = get_price("EURUSD")
    print(f"获取EURUSD价格结果: {result}")
    
    if result:
        print("✓ 成功获取价格数据")
        price_data = result[0]
        print(f"  - 合约代码: {price_data.get('symbol')}")
        print(f"  - 数据源: {price_data.get('source')}")
        print(f"  - 数据类型: {price_data.get('type')}")
        print(f"  - 最优买价: {price_data.get('best_bid')}")
        print(f"  - 最优卖价: {price_data.get('best_ask')}")
        print(f"  - 时间戳: {price_data.get('time')}")
    else:
        print("✗ 未获取到价格数据")


def test_get_price_with_fields():
    """测试带字段筛选的get_price功能"""
    print("\n" + "=" * 50)
    print("测试字段筛选功能")
    print("=" * 50)
    
    # 测试只获取特定字段
    fields = ['symbol', 'best_bid', 'best_ask', 'time']
    result = get_price("EURUSD", fields=fields)
    print(f"获取指定字段结果: {result}")
    
    if result:
        print("✓ 成功获取筛选后的价格数据")
        price_data = result[0]
        print(f"  - 返回的字段: {list(price_data.keys())}")
        print(f"  - 期望的字段: {fields}")
        
        # 检查是否只包含指定字段
        if set(price_data.keys()) == set(fields):
            print("✓ 字段筛选正确")
        else:
            print("✗ 字段筛选不正确")
    else:
        print("✗ 未获取到筛选后的价格数据")


def test_get_price_with_source():
    """测试指定数据源的get_price功能"""
    print("\n" + "=" * 50)
    print("测试指定数据源功能")
    print("=" * 50)
    
    # 测试指定数据源
    result = get_price("EURUSD", source="UBS_HO")
    print(f"指定UBS_HO数据源结果: {result}")
    
    if result:
        print("✓ 成功获取指定数据源的价格数据")
        price_data = result[0]
        print(f"  - 数据源: {price_data.get('source')}")
    else:
        print("✗ 未获取到指定数据源的价格数据")


def test_get_price_different_symbols():
    """测试不同合约的get_price功能"""
    print("\n" + "=" * 50)
    print("测试不同合约功能")
    print("=" * 50)
    
    symbols = ["EURUSD", "GBPUSD", "USDJPY", "USDCNY"]
    
    for symbol in symbols:
        print(f"\n测试合约: {symbol}")
        result = get_price(symbol)
        
        if result:
            price_data = result[0]
            print(f"  ✓ 成功获取{symbol}价格数据")
            print(f"    - 最优买价: {price_data.get('best_bid')}")
            print(f"    - 最优卖价: {price_data.get('best_ask')}")
        else:
            print(f"  ✗ 未获取到{symbol}价格数据")


def main():
    """主测试函数"""
    print("开始测试get_price函数")
    print("当前时间:", __import__('datetime').datetime.now())
    
    try:
        # 运行各项测试
        test_get_price_basic()
        test_get_price_with_fields()
        test_get_price_with_source()
        test_get_price_different_symbols()
        
        print("\n" + "=" * 50)
        print("测试完成")
        print("=" * 50)
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
