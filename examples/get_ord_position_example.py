"""
get_ord_position 函数使用示例

演示如何使用 get_ord_position 函数查询持仓信息
"""

import os
import sys
from datetime import datetime, timedelta
from decimal import Decimal
import uuid

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from custom_api.models import PositionSide, PositionStatus, PositionType
from custom_api.dao import get_position_dao
from custom_api.database.connection import init_database, cleanup_database
from custom_api.custom_api.pos.get_ord_position import get_ord_position


def create_sample_positions():
    """创建示例持仓数据"""
    
    position_dao = get_position_dao()
    
    # 生成唯一标识符
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    
    # 创建示例持仓
    sample_positions = [
        {
            'position_id': f'POS{timestamp}001_{unique_id}',
            'open_order_id': '216868121676222464',  # 使用API文档中的示例订单ID
            'channel_code': 'CHANNEL001',
            'symbol': 'EURUSDSP',
            'position_side': PositionSide.LONG,
            'position_status': PositionStatus.OPEN,
            'position_type': PositionType.NORMAL,
            'open_time': datetime.fromtimestamp(1530524400.101),  # API文档中的时间
            'open_price': Decimal('1.16064'),
            'current_price': Decimal('1.15850'),
            'total_quantity': Decimal('100000.0'),
            'available_quantity': Decimal('100000.0'),
            'frozen_quantity': Decimal('0.0'),
            'today_quantity': Decimal('100000.0'),
            'position_cost': Decimal('116064.0'),
            'market_value': Decimal('115850.0'),
            'unrealized_pnl': Decimal('-214.0'),
            'realized_pnl': Decimal('-2145.0'),
            'commission': Decimal('5.30'),
            'swap_fee': Decimal('0.0'),
            'is_active': True,
            'remark': 'API文档示例持仓'
        },
        {
            'position_id': f'POS{timestamp}002_{unique_id}',
            'open_order_id': f'ORD{timestamp}002',
            'channel_code': 'CHANNEL001',
            'symbol': 'XAUUSD',
            'position_side': PositionSide.SHORT,
            'position_status': PositionStatus.OPEN,
            'position_type': PositionType.NORMAL,
            'open_time': datetime.now() - timedelta(hours=1),
            'open_price': Decimal('2650.50'),
            'current_price': Decimal('2645.00'),
            'total_quantity': Decimal('1.0'),
            'available_quantity': Decimal('0.8'),
            'frozen_quantity': Decimal('0.2'),
            'today_quantity': Decimal('1.0'),
            'position_cost': Decimal('2650.50'),
            'market_value': Decimal('2645.00'),
            'unrealized_pnl': Decimal('5.50'),
            'realized_pnl': Decimal('0.0'),
            'commission': Decimal('5.30'),
            'swap_fee': Decimal('1.20'),
            'is_active': True,
            'remark': '黄金空头持仓'
        },
        {
            'position_id': f'POS{timestamp}003_{unique_id}',
            'open_order_id': f'ORD{timestamp}003',
            'channel_code': 'CHANNEL002',
            'symbol': 'GBPUSD',
            'position_side': PositionSide.LONG,
            'position_status': PositionStatus.OPEN,
            'position_type': PositionType.NORMAL,
            'open_time': datetime.now() - timedelta(minutes=30),
            'open_price': Decimal('1.2750'),
            'current_price': Decimal('1.2780'),
            'total_quantity': Decimal('50000.0'),
            'available_quantity': Decimal('50000.0'),
            'frozen_quantity': Decimal('0.0'),
            'today_quantity': Decimal('50000.0'),
            'position_cost': Decimal('63750.0'),
            'market_value': Decimal('63900.0'),
            'unrealized_pnl': Decimal('150.0'),
            'realized_pnl': Decimal('0.0'),
            'commission': Decimal('2.50'),
            'swap_fee': Decimal('0.0'),
            'is_active': True,
            'remark': '英美多头持仓'
        }
    ]
    
    # 批量创建持仓
    created_positions = position_dao.batch_create(sample_positions)
    print(f"创建了 {len(created_positions)} 个示例持仓")
    
    return created_positions


def main():
    """主函数"""
    
    print("=== get_ord_position 函数使用示例 ===\n")
    
    try:
        # 1. 初始化数据库
        print("1. 初始化数据库...")
        init_database()
        print("   ✓ 数据库初始化成功\n")
        
        # 2. 创建示例数据
        print("2. 创建示例持仓...")
        sample_positions = create_sample_positions()
        print()
        
        # 3. 基本用法示例
        print("3. 基本用法示例")
        print("-" * 50)
        
        # 获取所有未平仓持仓
        print("📊 获取所有未平仓持仓:")
        all_positions = get_ord_position()
        print(f"   共找到 {len(all_positions)} 个未平仓持仓")
        
        for i, pos in enumerate(all_positions[:3], 1):  # 只显示前3个
            print(f"   {i}. {pos['symbol']} - 数量: {pos['quantity']}, 盈亏: {pos['profit']:.2f}")
        
        print()
        
        # 4. 按订单ID查询
        print("4. 按订单ID查询")
        print("-" * 50)
        
        # 使用API文档中的示例订单ID
        order_id = "216868121676222464"
        print(f"🔍 查询订单ID: {order_id}")
        positions_by_order = get_ord_position(order_id=order_id)
        
        if positions_by_order:
            pos = positions_by_order[0]
            print(f"   找到持仓:")
            print(f"     合约: {pos['symbol']}")
            print(f"     持仓量: {pos['quantity']}")
            print(f"     方向: {'多头' if pos['posSide'] == 1 else '空头' if pos['posSide'] == 2 else '中性'}")
            print(f"     开仓价: {pos['costPrice']}")
            print(f"     盈亏: {pos['profit']:.2f}")
            print(f"     时间: {datetime.fromtimestamp(pos['time']/1000).strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("   未找到对应持仓")
        
        print()
        
        # 5. 按合约代码查询
        print("5. 按合约代码查询")
        print("-" * 50)
        
        symbols = ['EURUSDSP', 'XAUUSD', 'GBPUSD']
        for symbol in symbols:
            print(f"🔍 查询合约: {symbol}")
            positions_by_symbol = get_ord_position(symbol=symbol)
            
            if positions_by_symbol:
                print(f"   找到 {len(positions_by_symbol)} 个持仓:")
                for pos in positions_by_symbol:
                    side_text = {0: '中性', 1: '多头', 2: '空头'}[pos['posSide']]
                    print(f"     ID: {pos['id']}, {side_text}, 数量: {pos['quantity']}, 盈亏: {pos['profit']:.2f}")
            else:
                print("   未找到持仓")
            print()
        
        # 6. 数据格式详解
        print("6. 数据格式详解")
        print("-" * 50)
        
        if all_positions:
            pos = all_positions[0]
            print("📋 返回字段说明:")
            print(f"   id: {pos['id']} (持仓唯一编号)")
            print(f"   symbol: {pos['symbol']} (合约代码)")
            print(f"   frozenQuantity: {pos['frozenQuantity']} (冻结量)")
            print(f"   quantity: {pos['quantity']} (总持仓量)")
            print(f"   quantityTd: {pos['quantityTd']} (今日持仓量)")
            print(f"   posSide: {pos['posSide']} (头寸方向: 0-中性, 1-多头, 2-空头)")
            print(f"   profit: {pos['profit']:.2f} (总损益)")
            print(f"   value: {pos['value']:.2f} (持仓估值)")
            print(f"   costPrice: {pos['costPrice']} (开仓价格)")
            print(f"   unRealizedPL: {pos['unRealizedPL']:.2f} (未实现盈亏)")
            print(f"   realizedPL: {pos['realizedPL']:.2f} (已实现盈亏)")
            print(f"   washAmount: {pos['washAmount']:.2f} (持仓成本)")
            print(f"   time: {pos['time']} (开仓时间戳-毫秒)")
            
            # 时间转换示例
            dt = datetime.fromtimestamp(pos['time'] / 1000)
            print(f"   时间转换: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
        
        print()
        
        # 7. 错误处理示例
        print("7. 错误处理示例")
        print("-" * 50)
        
        # 查询不存在的订单
        print("🔍 查询不存在的订单ID:")
        empty_result = get_ord_position(order_id="NONEXISTENT123")
        print(f"   结果: {len(empty_result)} 个持仓 (返回空列表)")
        
        # 查询不存在的合约
        print("🔍 查询不存在的合约:")
        empty_result2 = get_ord_position(symbol="NONEXISTENT")
        print(f"   结果: {len(empty_result2)} 个持仓 (返回空列表)")
        
        # 参数类型错误
        print("🔍 参数类型错误处理:")
        try:
            get_ord_position(order_id=[])  # 错误的类型
        except ValueError as e:
            print(f"   捕获错误: {e}")
        
        print()
        
        # 8. 实际应用场景
        print("8. 实际应用场景")
        print("-" * 50)
        
        print("💡 常见使用场景:")
        print("   1. 风险监控: 定期获取所有持仓，计算总风险敞口")
        print("   2. 订单跟踪: 根据订单ID查询对应的持仓状态")
        print("   3. 合约分析: 按合约查询持仓，分析单一品种的风险")
        print("   4. 盈亏统计: 遍历所有持仓，计算总盈亏和收益率")
        print("   5. 持仓报告: 生成持仓明细报告")
        
        # 简单的风险统计示例
        print("\n📊 风险统计示例:")
        total_positions = len(all_positions)
        total_profit = sum(pos['profit'] for pos in all_positions)
        profitable_count = sum(1 for pos in all_positions if pos['profit'] > 0)
        
        print(f"   总持仓数: {total_positions}")
        print(f"   总盈亏: {total_profit:.2f}")
        print(f"   盈利持仓数: {profitable_count}")
        print(f"   胜率: {profitable_count/total_positions*100:.1f}%" if total_positions > 0 else "   胜率: 0%")
        
        print("\n=== 示例执行完成 ===")
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理数据库资源
        cleanup_database()


if __name__ == "__main__":
    main()
